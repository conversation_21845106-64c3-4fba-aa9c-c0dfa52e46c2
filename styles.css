/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --background-light: #f9fafb;
    --background-white: #ffffff;
    --border-color: #e5e7eb;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-white);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-xl);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border-radius: var(--radius-md);
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo img {
    height: 40px;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-cta {
    background-color: var(--primary-color);
    color: white !important;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-normal);
}

.nav-cta:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger .bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition-fast);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.8), rgba(16, 185, 129, 0.6));
}

.hero-content {
    position: relative;
    text-align: center;
    color: white;
    z-index: 1;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    max-width: 800px;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    color: rgba(255, 255, 255, 0.9);
}

.hero-btn {
    font-size: var(--font-size-lg);
    padding: var(--spacing-lg) var(--spacing-2xl);
}

/* About Section */
.about {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-item {
    margin-bottom: var(--spacing-2xl);
}

.about-item h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

/* Carousel */
.about-carousel {
    position: relative;
}

.carousel-container {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-large);
}

.carousel-slide {
    display: none;
    position: relative;
}

.carousel-slide.active {
    display: block;
}

.carousel-slide img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.carousel-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: var(--spacing-xl);
    font-weight: 500;
}

.carousel-controls {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 var(--spacing-md);
}

.carousel-btn {
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.carousel-btn:hover {
    background-color: white;
    transform: scale(1.1);
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-lg);
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--border-color);
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.indicator.active {
    background-color: var(--primary-color);
}

/* Products Section */
.products {
    padding: var(--spacing-3xl) 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.product-card {
    background-color: var(--background-white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-light);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
}

.product-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.product-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.product-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.product-description {
    margin-bottom: var(--spacing-lg);
}

.product-features {
    list-style: none;
    margin-bottom: var(--spacing-xl);
}

.product-features li {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: var(--spacing-lg);
}

.product-features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

/* Services Section */
.services {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-2xl);
}

.service-card {
    background-color: var(--background-white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.service-icon i {
    font-size: var(--font-size-2xl);
    color: white;
}

.service-title {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

/* Research Section */
.research {
    padding: var(--spacing-3xl) 0;
}

.research-timeline {
    max-width: 800px;
    margin: 0 auto;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: "";
    position: absolute;
    left: 30px;
    top: 80px;
    width: 2px;
    height: calc(100% + var(--spacing-lg));
    background-color: var(--border-color);
}

.timeline-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.timeline-icon i {
    font-size: var(--font-size-xl);
    color: white;
}

.timeline-content h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

/* Testimonials Section */
.testimonials {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-light);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-2xl);
}

.testimonial-card {
    background-color: var(--background-white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-light);
    transition: all var(--transition-normal);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.testimonial-content {
    margin-bottom: var(--spacing-xl);
}

.testimonial-content p {
    font-style: italic;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    position: relative;
}

.testimonial-content p::before {
    content: """;
    font-size: var(--font-size-4xl);
    color: var(--primary-color);
    position: absolute;
    left: -20px;
    top: -10px;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.author-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.author-info p {
    color: var(--text-light);
    margin: 0;
    font-size: var(--font-size-sm);
}

/* Contact Section */
.contact {
    padding: var(--spacing-3xl) 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: var(--font-size-lg);
    color: white;
}

.contact-details h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.contact-details p {
    margin: 0;
    color: var(--text-secondary);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.social-link {
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all var(--transition-normal);
}

.social-link:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
}

/* Contact Form */
.contact-form {
    background-color: var(--background-light);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-normal);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Footer */
.footer {
    background-color: var(--text-primary);
    color: white;
    padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-2xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-section h3 {
    color: white;
    margin-bottom: var(--spacing-lg);
}

.footer-logo img {
    height: 40px;
    margin-bottom: var(--spacing-md);
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-sm);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: var(--spacing-xl);
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--spacing-md);
    }

    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }
}

@media (max-width: 768px) {
    /* Navigation */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-medium);
        padding: var(--spacing-xl) 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: var(--spacing-md) 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    /* Hero */
    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    /* Sections */
    .section-header {
        margin-bottom: var(--spacing-2xl);
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }

    /* Products and Services */
    .products-grid,
    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    /* Timeline */
    .timeline-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .timeline-item::after {
        display: none;
    }

    /* Testimonials */
    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero-title {
        font-size: var(--font-size-2xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-base);
    }

    .carousel-slide img {
        height: 250px;
    }

    .product-card,
    .service-card,
    .testimonial-card {
        padding: var(--spacing-lg);
    }

    .contact-form {
        padding: var(--spacing-lg);
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling offset for fixed navbar */
section {
    scroll-margin-top: 80px;
}
