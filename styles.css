/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Enhanced Color Palette */
  --primary-color: #0066ff;
  --primary-dark: #0052cc;
  --primary-light: #3385ff;
  --secondary-color: #00d4aa;
  --secondary-dark: #00b894;
  --accent-color: #ff6b35;
  --accent-light: #ff8c69;
  --success-color: #00b894;
  --warning-color: #fdcb6e;
  --error-color: #e17055;

  /* Advanced Text Colors */
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --text-light: #718096;
  --text-muted: #a0aec0;
  --text-white: #ffffff;

  /* Sophisticated Backgrounds */
  --background-primary: #ffffff;
  --background-secondary: #f7fafc;
  --background-dark: #1a202c;
  --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --background-hero: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.9) 0%,
    rgba(0, 212, 170, 0.8) 100%
  );

  /* Enhanced Shadows */
  --shadow-xs: 0 0 0 1px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(0, 102, 255, 0.3);
  --shadow-glow-green: 0 0 20px rgba(0, 212, 170, 0.3);

  /* Typography */
  --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Enhanced Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Additional Typography */
  --font-size-6xl: 3.75rem;
  --font-size-7xl: 4.5rem;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Enhanced Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;

  /* Modern Border Radius */
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1050;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.7;
  color: var(--text-primary);
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* Enhanced Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: var(--spacing-4);
  letter-spacing: -0.025em;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

h1 {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-extrabold);
  letter-spacing: -0.05em;
}

h2 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
}

h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
}

h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
}

p {
  margin-bottom: var(--spacing-4);
  color: var(--text-secondary);
  line-height: 1.8;
  font-weight: var(--font-weight-normal);
}

/* Enhanced Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  text-decoration: none;
  border-radius: var(--radius-2xl);
  border: none;
  cursor: pointer;
  transition: var(--transition-bounce);
  text-align: center;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
  text-transform: uppercase;
  font-size: var(--font-size-sm);
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: white;
  box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Enhanced Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  z-index: var(--z-fixed);
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: var(--shadow-lg);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) var(--spacing-6);
  max-width: 1200px;
  margin: 0 auto;
}

.nav-logo img {
  height: 45px;
  transition: var(--transition-normal);
}

.nav-logo img:hover {
  transform: scale(1.05);
}

.nav-menu {
  display: flex;
  list-style: none;
  align-items: center;
  gap: var(--spacing-8);
}

.nav-link {
  text-decoration: none;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  letter-spacing: 0.025em;
  text-transform: uppercase;
  position: relative;
  transition: var(--transition-normal);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

.nav-link:hover {
  color: var(--primary-color);
  transform: translateY(-1px);
}

.nav-cta {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-light)
  );
  color: white !important;
  padding: var(--spacing-3) var(--spacing-6);
  border-radius: var(--radius-full);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  letter-spacing: 0.025em;
  text-transform: uppercase;
  transition: var(--transition-bounce);
  box-shadow: var(--shadow-md);
}

.nav-cta:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.hamburger .bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  margin: 3px 0;
  transition: var(--transition-fast);
}

/* Enhanced Hero Section */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 30% 70%,
      rgba(0, 102, 255, 0.3) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 70% 30%,
      rgba(0, 212, 170, 0.3) 0%,
      transparent 50%
    );
  z-index: 2;
  pointer-events: none;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scale(1.1);
  animation: heroZoom 20s ease-in-out infinite alternate;
}

@keyframes heroZoom {
  0% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1.2);
  }
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.85) 0%,
    rgba(0, 212, 170, 0.75) 100%
  );
  z-index: 1;
}

.hero-content {
  position: relative;
  text-align: center;
  color: white;
  z-index: 3;
  animation: heroFadeInUp 1s ease-out;
}

@keyframes heroFadeInUp {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-title {
  font-size: var(--font-size-6xl);
  font-weight: var(--font-weight-extrabold);
  margin-bottom: var(--spacing-6);
  max-width: 900px;
  line-height: 1.1;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-10);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  color: rgba(255, 255, 255, 0.95);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero-btn {
  font-size: var(--font-size-base);
  padding: var(--spacing-5) var(--spacing-10);
  margin-top: var(--spacing-4);
  animation: heroBtnFloat 3s ease-in-out infinite;
}

@keyframes heroBtnFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* About Section */
.about {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-light);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.about-item {
  margin-bottom: var(--spacing-2xl);
}

.about-item h3 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

/* Carousel */
.about-carousel {
  position: relative;
}

.carousel-container {
  position: relative;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-large);
}

.carousel-slide {
  display: none;
  position: relative;
}

.carousel-slide.active {
  display: block;
}

.carousel-slide img {
  width: 100%;
  height: 400px;
  object-fit: cover;
}

.carousel-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: var(--spacing-xl);
  font-weight: 500;
}

.carousel-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 var(--spacing-md);
}

.carousel-btn {
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
}

.carousel-btn:hover {
  background-color: white;
  transform: scale(1.1);
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--border-color);
  cursor: pointer;
  transition: background-color var(--transition-normal);
}

.indicator.active {
  background-color: var(--primary-color);
}

/* Products Section */
.products {
  padding: var(--spacing-3xl) 0;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-2xl);
}

.product-card {
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.7)
  );
  backdrop-filter: blur(20px);
  border-radius: var(--radius-3xl);
  padding: var(--spacing-8);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-bounce);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.product-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
}

.product-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.product-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-6);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-bounce);
}

.product-card:hover .product-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.product-icon i {
  font-size: var(--font-size-3xl);
  color: white;
}

.product-title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.product-description {
  margin-bottom: var(--spacing-lg);
}

.product-features {
  list-style: none;
  margin-bottom: var(--spacing-xl);
}

.product-features li {
  padding: var(--spacing-xs) 0;
  color: var(--text-secondary);
  position: relative;
  padding-left: var(--spacing-lg);
}

.product-features li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--secondary-color);
  font-weight: bold;
}

/* Services Section */
.services {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-light);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-2xl);
}

.service-card {
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.8)
  );
  backdrop-filter: blur(20px);
  border-radius: var(--radius-3xl);
  padding: var(--spacing-8);
  text-align: center;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-bounce);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.05),
    rgba(0, 212, 170, 0.05)
  );
  opacity: 0;
  transition: var(--transition-normal);
}

.service-card:hover::before {
  opacity: 1;
}

.service-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-2xl), var(--shadow-glow-green);
}

.service-icon {
  width: 100px;
  height: 100px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-6);
  box-shadow: var(--shadow-xl);
  transition: var(--transition-bounce);
  position: relative;
}

.service-card:hover .service-icon {
  transform: scale(1.1) rotate(-5deg);
  box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.service-icon i {
  font-size: var(--font-size-3xl);
  color: white;
}

.service-title {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

/* Research Section */
.research {
  padding: var(--spacing-3xl) 0;
}

.research-timeline {
  max-width: 800px;
  margin: 0 auto;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 30px;
  top: 80px;
  width: 2px;
  height: calc(100% + var(--spacing-lg));
  background-color: var(--border-color);
}

.timeline-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--accent-color)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.timeline-icon i {
  font-size: var(--font-size-xl);
  color: white;
}

.timeline-content h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

/* Testimonials Section */
.testimonials {
  padding: var(--spacing-3xl) 0;
  background-color: var(--background-light);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-2xl);
}

.testimonial-card {
  background-color: var(--background-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-light);
  transition: all var(--transition-normal);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.testimonial-content {
  margin-bottom: var(--spacing-xl);
}

.testimonial-content p {
  font-style: italic;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  position: relative;
}

.testimonial-content p::before {
  content: '"';
  font-size: var(--font-size-4xl);
  color: var(--primary-color);
  position: absolute;
  left: -20px;
  top: -10px;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.author-info p {
  color: var(--text-light);
  margin: 0;
  font-size: var(--font-size-sm);
}

/* Contact Section */
.contact {
  padding: var(--spacing-3xl) 0;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
}

.contact-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.contact-icon i {
  font-size: var(--font-size-lg);
  color: white;
}

.contact-details h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.contact-details p {
  margin: 0;
  color: var(--text-secondary);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.social-link {
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all var(--transition-normal);
}

.social-link:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

/* Contact Form */
.contact-form {
  background-color: var(--background-light);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  transition: border-color var(--transition-normal);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Footer */
.footer {
  background-color: var(--text-primary);
  color: white;
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-section h3 {
  color: white;
  margin-bottom: var(--spacing-lg);
}

.footer-logo img {
  height: 40px;
  margin-bottom: var(--spacing-md);
}

.footer-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.footer-links a:hover {
  color: white;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: var(--spacing-xl);
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }
}

@media (max-width: 768px) {
  /* Enhanced Mobile Navigation */
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 80px;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    width: 100%;
    text-align: center;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-8) 0;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: var(--spacing-4) 0;
  }

  .nav-link {
    font-size: var(--font-size-base);
    padding: var(--spacing-3) 0;
  }

  .hamburger {
    display: flex;
    z-index: var(--z-modal);
  }

  .hamburger.active .bar:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .hamburger.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  /* Enhanced Mobile Hero */
  .hero-title {
    font-size: var(--font-size-4xl);
    line-height: 1.2;
    margin-bottom: var(--spacing-4);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-8);
    padding: 0 var(--spacing-4);
  }

  .hero-btn {
    padding: var(--spacing-4) var(--spacing-8);
    font-size: var(--font-size-sm);
  }

  /* Sections */
  .section-header {
    margin-bottom: var(--spacing-2xl);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  /* Products and Services */
  .products-grid,
  .services-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  /* Timeline */
  .timeline-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .timeline-item::after {
    display: none;
  }

  /* Testimonials */
  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  /* Footer */
  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-4);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
    line-height: 1.1;
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
    padding: 0 var(--spacing-2);
  }

  .carousel-slide img {
    height: 250px;
  }

  .product-card,
  .service-card,
  .testimonial-card {
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-4);
  }

  .contact-form {
    padding: var(--spacing-6);
  }

  .section-header {
    margin-bottom: var(--spacing-8);
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .products-grid,
  .services-grid,
  .testimonials-grid {
    gap: var(--spacing-4);
  }

  .product-icon,
  .service-icon {
    width: 70px;
    height: 70px;
  }

  .product-icon i,
  .service-icon i {
    font-size: var(--font-size-2xl);
  }
}

/* Utility Classes */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: var(--spacing-xs);
}
.mb-2 {
  margin-bottom: var(--spacing-sm);
}
.mb-3 {
  margin-bottom: var(--spacing-md);
}
.mb-4 {
  margin-bottom: var(--spacing-lg);
}
.mb-5 {
  margin-bottom: var(--spacing-xl);
}

.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: var(--spacing-xs);
}
.mt-2 {
  margin-top: var(--spacing-sm);
}
.mt-3 {
  margin-top: var(--spacing-md);
}
.mt-4 {
  margin-top: var(--spacing-lg);
}
.mt-5 {
  margin-top: var(--spacing-xl);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling offset for fixed navbar */
section {
  scroll-margin-top: 80px;
}

/* Enhanced Scroll Animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Floating Elements */
.floating {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Pulse Animation */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass Morphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Neon Glow Effect */
.neon-glow {
  box-shadow: 0 0 5px var(--primary-color), 0 0 10px var(--primary-color),
    0 0 15px var(--primary-color), 0 0 20px var(--primary-color);
}

/* Loading Animation */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    135deg,
    var(--primary-dark),
    var(--secondary-dark)
  );
}

/* Selection Styling */
::selection {
  background: var(--primary-color);
  color: white;
}

::-moz-selection {
  background: var(--primary-color);
  color: white;
}
